/* Base styling and consistency */
:root {
  --primary-color: #853dcc;
  --primary-hover: #6f32a8;
  --secondary-color: #0a2e33;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --light-bg: #e9d6ff;
  --border-color: #dee2e6;
  --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Apply Poppins font globally */
body {
  font-family: "Poppins", sans-serif;
}

/* Container consistency - match navbar width */
.container {
  max-width: 1140px;
}

/* Enhanced card styling */
.card {
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.card-header {
  background-color: var(--light-bg);
  border-bottom: 1px solid var(--border-color);
  font-weight: 600;
  padding: 1rem 1.25rem;
}

.card-body {
  padding: 1.5rem;
}

/* Service selection improvements */
.services-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--border-color);
  border-radius: 0.375rem;
  padding: 1rem;
  background-color: #fff;
}

.service-checkbox-item {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f1f3f4;
}

.service-checkbox-item:last-child {
  border-bottom: none;
}

.service-details {
  border-left: 4px solid var(--primary-color);
  background-color: #f8f9fa;
  border-radius: 0.375rem;
  margin-top: 1rem;
  padding: 1rem;
  display: none;
}

.service-details.show {
  display: block;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.service-details .card-header {
  background-color: transparent;
  border-bottom: 1px solid var(--border-color);
  padding: 0.75rem 0;
  margin-bottom: 1rem;
}

/* Form enhancements */
.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #495057;
}

.form-control,
.form-select {
  border-radius: 0.375rem;
  border: 1px solid #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus,
.form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Button enhancements */
.btn {
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.15s ease-in-out;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
  transform: translateY(-1px);
}

.btn-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

/* Table enhancements */
.table {
  margin-bottom: 0;
}

.table th {
  border-top: none;
  font-weight: 700;
  color: black;
  padding: 1rem 0.75rem;
}

.table td {
  padding: 0.75rem;
  vertical-align: middle;
}

.table-hover tbody tr:hover {
  background-color: #dbfdff;
}

.table-responsive {
  border-radius: 0.5rem;
  box-shadow: var(--shadow);
  /* Removed max-height and overflow-y to prevent double scrollbars */
}

/* Catalog table specific styling - simplified */
.catalog-table-container {
  max-height: 500px;
  overflow-y: auto;
  border-radius: 0.5rem;
}

/* Remove table-responsive wrapper styling for catalog to prevent double scrollbars */
.catalog-table-container .table-responsive {
  box-shadow: none;
  border-radius: 0;
  max-height: none;
  overflow: visible;
}

/* Submissions table - no height restriction since we have pagination */
.submissions-table-container .table-responsive {
  max-height: none;
  overflow-x: auto;
  overflow-y: visible;
}

/* Alert enhancements */
.alert {
  border-radius: 0.5rem;
  border: none;
  margin-bottom: 1.5rem;
  box-shadow: var(--shadow);
}

.alert-success {
  background-color: #d1edff;
  color: #0c5460;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
}

/* Navbar enhancements */
.navbar-brand {
  font-weight: 600;
  font-size: 1.5rem;
}

.nav-link {
  font-weight: 500;
  transition: color 0.15s ease-in-out;
}

.nav-link:hover {
  color: #fff !important;
}

.nav-link.active {
  color: #fff !important;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0.375rem;
}

/* Custom navbar and footer background */
.navbar-dark.bg-dark,
footer.bg-dark {
  background-color: var(--primary-color) !important;
}

/* Deadline notification styles */
.deadline-notification {
  border-left: 4px solid #ffc107;
  background-color: #fff3cd;
  border-radius: 0.375rem;
}

.deadline-notification.critical {
  border-left-color: #dc3545;
  background-color: #f8d7da;
}

/* Table row highlighting for deadlines */
.table-warning {
  background-color: rgba(255, 193, 7, 0.1) !important;
}

.table-danger {
  background-color: rgba(220, 53, 69, 0.1) !important;
}

.table-secondary {
  background-color: rgba(108, 117, 125, 0.1) !important;
}

/* Badge enhancements */
.badge {
  font-size: 0.75em;
  font-weight: 500;
}

/* Alert enhancements for deadline notifications */
.alert-warning {
  border: 1px solid #ffc107;
  background-color: #fff3cd;
  color: #856404;
}

.alert-warning .fas {
  color: #856404;
}

/* Pagination enhancements */
.pagination {
  margin-top: 2rem;
  justify-content: center;
}

.page-link {
  border-radius: 0.375rem;
  margin: 0 0.125rem;
  border: 1px solid var(--border-color);
  color: var(--primary-color);
}

.page-link:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.page-item.active .page-link {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Filter section styling */
.filters-card {
  background-color: #f8f9fa;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

/* Statistics styling */
.stats-container {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-hover)
  );
  color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .card-body {
    padding: 1rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .btn-group .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
}

/* Utility classes */
.text-primary {
  color: var(--primary-color) !important;
}

.bg-light-custom {
  background-color: #f8f9fa !important;
}

.border-left-primary {
  border-left: 4px solid var(--primary-color) !important;
}

/* Loading states */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Form validation styling */
.is-invalid {
  border-color: var(--danger-color);
}

.invalid-feedback {
  color: var(--danger-color);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}
