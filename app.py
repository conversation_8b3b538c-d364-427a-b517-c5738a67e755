from flask import (
    Flask,
    render_template,
    request,
    redirect,
    url_for,
    flash,
    jsonify,
    send_file,
)
from datetime import datetime, date, timedelta
from sqlalchemy import or_, and_
import os
import pandas as pd
from io import BytesIO

from models import db, Company, ServiceCatalog, ServiceOrder

app = Flask(__name__)
app.config["SECRET_KEY"] = "your-secret-key-here"
app.config["SQLALCHEMY_DATABASE_URI"] = "sqlite:///service_orders.db"
app.config["SQLALCHEMY_TRACK_MODIFICATIONS"] = False

db.init_app(app)


def get_approaching_deadlines(days_threshold=15):
    """Get submissions with end dates approaching within the specified number of days."""
    today = date.today()
    threshold_date = today + timedelta(days=days_threshold)

    approaching_orders = ServiceOrder.query.filter(
        and_(
            ServiceOrder.end_date >= today,  # Not expired yet
            ServiceOrder.end_date <= threshold_date,  # Within threshold
        )
    ).all()

    return approaching_orders


@app.route("/")
def index():
    return redirect(url_for("submissions"))


@app.route("/new")
def new_order():
    companies = Company.query.all()
    services = ServiceCatalog.query.all()
    return render_template("index.html", companies=companies, services=services)


@app.route("/submissions")
def submissions():
    # Get filter parameters
    company_filter = request.args.get("company", "")
    service_filter = request.args.get("service", "")
    search_query = request.args.get("search", "")
    date_from = request.args.get("date_from", "")
    date_to = request.args.get("date_to", "")
    sort_by = request.args.get("sort_by", "id")  # Changed default to 'id'
    sort_order = request.args.get("sort_order", "asc")  # Changed default to 'asc'

    # Base query
    query = ServiceOrder.query

    # Apply filters
    if company_filter:
        query = query.filter(ServiceOrder.company_name == company_filter)

    if service_filter:
        query = query.filter(ServiceOrder.service_name == service_filter)

    if search_query:
        query = query.filter(
            or_(
                ServiceOrder.company_name.contains(search_query),
                ServiceOrder.service_name.contains(search_query),
            )
        )

    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, "%Y-%m-%d").date()
            query = query.filter(ServiceOrder.start_date >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, "%Y-%m-%d").date()
            query = query.filter(ServiceOrder.end_date <= date_to_obj)
        except ValueError:
            pass

    # Apply sorting
    if sort_by == "id":
        order_column = ServiceOrder.id
    elif sort_by == "company_name":
        order_column = ServiceOrder.company_name
    elif sort_by == "service_name":
        order_column = ServiceOrder.service_name
    elif sort_by == "price":
        order_column = ServiceOrder.price
    elif sort_by == "start_date":
        order_column = ServiceOrder.start_date
    elif sort_by == "end_date":
        order_column = ServiceOrder.end_date
    else:
        order_column = ServiceOrder.created_at

    if sort_order == "asc":
        query = query.order_by(order_column.asc())
    else:
        query = query.order_by(order_column.desc())

    # Get paginated results
    page = request.args.get("page", 1, type=int)
    per_page = 7
    orders = query.paginate(page=page, per_page=per_page, error_out=False)

    # Get unique companies and services for filter dropdowns
    companies = db.session.query(ServiceOrder.company_name).distinct().all()
    companies = [c[0] for c in companies]

    services = db.session.query(ServiceOrder.service_name).distinct().all()
    services = [s[0] for s in services]

    # Calculate summary statistics
    total_orders = ServiceOrder.query.count()
    total_value = db.session.query(db.func.sum(ServiceOrder.price)).scalar() or 0

    # Get approaching deadlines for notifications
    approaching_deadlines = get_approaching_deadlines()

    return render_template(
        "submissions.html",
        orders=orders,
        companies=companies,
        services=services,
        total_orders=total_orders,
        total_value=total_value,
        approaching_deadlines=approaching_deadlines,
        filters={
            "company": company_filter,
            "service": service_filter,
            "search": search_query,
            "date_from": date_from,
            "date_to": date_to,
            "sort_by": sort_by,
            "sort_order": sort_order,
        },
    )


@app.route("/catalog")
def catalog():
    services = ServiceCatalog.query.all()
    return render_template("catalog.html", services=services)


@app.route("/add_service", methods=["POST"])
def add_service():
    service_name = request.form.get("service_name")
    if service_name:
        service = ServiceCatalog(name=service_name)
        db.session.add(service)
        db.session.commit()
        flash("Service added successfully!", "success")
    return redirect(url_for("catalog"))


@app.route("/delete_service/<int:service_id>")
def delete_service(service_id):
    service = ServiceCatalog.query.get_or_404(service_id)
    db.session.delete(service)
    db.session.commit()
    flash("Service deleted successfully!", "success")
    return redirect(url_for("catalog"))


@app.route("/delete_order/<int:order_id>")
def delete_order(order_id):
    order = ServiceOrder.query.get_or_404(order_id)
    db.session.delete(order)
    db.session.commit()
    flash("Services submission deleted successfully!", "success")
    return redirect(url_for("submissions"))


@app.route("/submit", methods=["POST"])
def submit_order():
    company_name = request.form.get("company_name")
    selected_services = request.form.getlist("services")

    if not company_name or not selected_services:
        flash("Please select a company and at least one service.", "error")
        return redirect(url_for("new_order"))

    # Create company if it doesn't exist
    company = Company.query.filter_by(name=company_name).first()
    if not company:
        company = Company(name=company_name)
        db.session.add(company)
        db.session.commit()

    # Process each selected service
    for service_id in selected_services:
        service = ServiceCatalog.query.get(service_id)
        if service:
            price = request.form.get(f"price_{service_id}")
            start_date = request.form.get(f"start_date_{service_id}")
            end_date = request.form.get(f"end_date_{service_id}")

            if price and start_date and end_date:
                order = ServiceOrder(
                    company_name=company_name,
                    service_name=service.name,
                    price=float(price),
                    start_date=datetime.strptime(start_date, "%d-%m-%Y").date(),
                    end_date=datetime.strptime(end_date, "%d-%m-%Y").date(),
                )
                db.session.add(order)

    db.session.commit()
    flash("Services submission submitted successfully!", "success")
    return redirect(url_for("submissions"))


@app.route("/edit_order/<int:order_id>", methods=["GET", "POST"])
def edit_order(order_id):
    order = ServiceOrder.query.get_or_404(order_id)

    if request.method == "POST":
        # Update order details
        order.company_name = request.form.get("company_name")
        order.service_name = request.form.get("service_name")
        order.price = float(request.form.get("price"))

        start_date = request.form.get("start_date")
        end_date = request.form.get("end_date")

        order.start_date = datetime.strptime(start_date, "%d-%m-%Y").date()
        order.end_date = datetime.strptime(end_date, "%d-%m-%Y").date()

        db.session.commit()
        flash("Services submission updated successfully!", "success")
        return redirect(url_for("submissions"))

    # GET request - show edit form
    companies = Company.query.all()
    services = ServiceCatalog.query.all()

    return render_template(
        "edit_order.html", order=order, companies=companies, services=services
    )


@app.route("/api/notifications")
def get_notifications():
    """API endpoint to get deadline notifications as JSON."""
    approaching_deadlines = get_approaching_deadlines()

    notifications = []
    for order in approaching_deadlines:
        notifications.append(
            {
                "id": order.id,
                "company_name": order.company_name,
                "service_name": order.service_name,
                "end_date": order.end_date.strftime("%d-%m-%Y"),
                "days_until_end": order.days_until_end,
                "deadline_status": order.deadline_status,
            }
        )

    return jsonify({"count": len(notifications), "notifications": notifications})


@app.route("/export_excel")
def export_excel():
    # Get filter parameters (same as submissions route)
    company_filter = request.args.get("company", "")
    service_filter = request.args.get("service", "")
    search_query = request.args.get("search", "")
    date_from = request.args.get("date_from", "")
    date_to = request.args.get("date_to", "")
    sort_by = request.args.get("sort_by", "id")
    sort_order = request.args.get("sort_order", "asc")

    # Base query (same filtering logic as submissions)
    query = ServiceOrder.query

    # Apply filters
    if company_filter:
        query = query.filter(ServiceOrder.company_name == company_filter)
    if service_filter:
        query = query.filter(ServiceOrder.service_name == service_filter)
    if search_query:
        query = query.filter(
            or_(
                ServiceOrder.company_name.contains(search_query),
                ServiceOrder.service_name.contains(search_query),
            )
        )
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, "%Y-%m-%d").date()
            query = query.filter(ServiceOrder.start_date >= date_from_obj)
        except ValueError:
            pass
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, "%Y-%m-%d").date()
            query = query.filter(ServiceOrder.end_date <= date_to_obj)
        except ValueError:
            pass

    # Apply sorting
    if sort_by == "id":
        order_column = ServiceOrder.id
    elif sort_by == "company_name":
        order_column = ServiceOrder.company_name
    elif sort_by == "service_name":
        order_column = ServiceOrder.service_name
    elif sort_by == "price":
        order_column = ServiceOrder.price
    elif sort_by == "start_date":
        order_column = ServiceOrder.start_date
    elif sort_by == "end_date":
        order_column = ServiceOrder.end_date
    else:
        order_column = ServiceOrder.created_at

    if sort_order == "asc":
        query = query.order_by(order_column.asc())
    else:
        query = query.order_by(order_column.desc())

    # Get all results (no pagination for export)
    orders = query.all()

    # Create DataFrame
    data = []
    for order in orders:
        data.append(
            {
                "ID": order.id,
                "Company": order.company_name,
                "Service": order.service_name,
                "Price (€)": f"€{order.price:.2f}",
                "Start Date": order.start_date.strftime("%d-%m-%Y"),
                "End Date": order.end_date.strftime("%d-%m-%Y"),
                "Created": order.created_at.strftime("%d-%m-%Y %H:%M"),
            }
        )

    df = pd.DataFrame(data)

    # Create Excel file in memory
    output = BytesIO()
    with pd.ExcelWriter(output, engine="openpyxl") as writer:
        df.to_excel(writer, sheet_name="Services Submissions", index=False)

        # Auto-adjust column widths
        worksheet = writer.sheets["Services Submissions"]
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    output.seek(0)

    # Generate filename with timestamp
    timestamp = datetime.now().strftime("%d-%m-%Y")
    filename = f"apifon_services_{timestamp}.xlsx"

    return send_file(
        output,
        mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        as_attachment=True,
        download_name=filename,
    )


if __name__ == "__main__":
    with app.app_context():
        db.create_all()

    app.run(debug=True)
