from flask_sqlalchemy import SQLAlchemy
from datetime import date

db = SQLAlchemy()


class Company(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)

    def __repr__(self):
        return f"<Company {self.name}>"


class ServiceCatalog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)

    def __repr__(self):
        return f"<ServiceCatalog {self.name}>"


class ServiceOrder(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    company_name = db.Column(db.String(100), nullable=False)
    service_name = db.Column(db.String(100), nullable=False)
    price = db.Column(db.Float, nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    created_at = db.Column(db.DateTime, default=db.func.current_timestamp())

    def __repr__(self):
        return f"<ServiceOrder {self.company_name} - {self.service_name}>"

    @property
    def days_until_end(self):
        """Calculate the number of days until the end date."""
        today = date.today()
        if self.end_date < today:
            return 0  # Already expired
        return (self.end_date - today).days

    @property
    def is_approaching_deadline(self, threshold_days=15):
        """Check if the submission is approaching its deadline."""
        return 0 < self.days_until_end <= threshold_days

    @property
    def deadline_status(self):
        """Get the deadline status for styling purposes."""
        days_left = self.days_until_end
        if days_left == 0:
            return "expired"
        elif days_left <= 7:
            return "critical"  # 7 days or less
        elif days_left <= 15:
            return "warning"  # 8-15 days
        else:
            return "normal"  # More than 15 days
