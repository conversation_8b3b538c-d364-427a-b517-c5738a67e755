<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% block title %}Apifon Services Management{% endblock %}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='style.css') }}"
    />
  </head>
  <body class="d-flex flex-column min-vh-100 bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
      <div class="container">
        <a class="navbar-brand" href="{{ url_for('index') }}"
          >Apifon Services</a
        >
        <div class="navbar-nav gap-3">
          <a
            class="nav-link {{ 'active' if request.endpoint == 'new_order' else '' }}"
            href="{{ url_for('new_order') }}"
            >New Submission</a
          >
          <a
            class="nav-link {{ 'active' if request.endpoint == 'submissions' else '' }}"
            href="{{ url_for('submissions') }}"
            >Services List
            <span
              id="notification-badge"
              class="badge bg-warning ms-1"
              style="display: none"
              >0</span
            >
          </a>
          <a
            class="nav-link {{ 'active' if request.endpoint == 'catalog' else '' }}"
            href="{{ url_for('catalog') }}"
            >Manage Services</a
          >
        </div>
      </div>
    </nav>

    <div class="container mt-4 flex-grow-1">
      {% with messages = get_flashed_messages(with_categories=true) %} {% if
      messages %} {% for category, message in messages %}
      <div
        class="alert alert-{{ 'danger' if category == 'error' else 'success' }} alert-dismissible fade show"
      >
        {{ message }}
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="alert"
        ></button>
      </div>
      {% endfor %} {% endif %} {% endwith %} {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white mt-auto py-4">
      <div class="container">
        <div class="row">
          <div class="col-md-6">
            <h6 class="mb-2">Apifon Services</h6>
            <p class="text-white mb-0">
              Streamline Apifon services and catalog management
            </p>
          </div>
          <div class="col-md-6 text-md-end">
            <p class="text-white mb-0">
              © {{ moment().format('YYYY') if moment else '2025' }} Apifon
              Services. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Notification Badge Script -->
    <script>
      // Function to update notification badge
      function updateNotificationBadge() {
        fetch("/api/notifications")
          .then((response) => response.json())
          .then((data) => {
            const badge = document.getElementById("notification-badge");
            if (data.count > 0) {
              badge.textContent = data.count;
              badge.style.display = "inline";
              badge.title = `${data.count} submission(s) with approaching deadlines`;
            } else {
              badge.style.display = "none";
            }
          })
          .catch((error) => {
            console.error("Error fetching notifications:", error);
          });
      }

      // Update badge on page load
      document.addEventListener("DOMContentLoaded", updateNotificationBadge);

      // Update badge every 5 minutes
      setInterval(updateNotificationBadge, 300000);
    </script>

    {% block scripts %}{% endblock %}
  </body>
</html>
