
{% extends "base.html" %}

{% block title %}Service Order Submissions{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Deadline Notifications -->
        {% if approaching_deadlines %}
        <div class="alert alert-warning alert-dismissible fade show mb-4" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <div>
                    <strong>Deadline Alert!</strong> You have {{ approaching_deadlines|length }} submission(s) with end dates approaching within 15 days.
                   
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endif %}

        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>Services Submissions</h2>
            <div class="d-flex align-items-center gap-3">
                <div class="text-muted">
                    <small>Total Submissions: {{ total_orders }} | Total Value: €{{ "%.2f"|format(total_value) }}</small>
                </div>
                <a href="{{ url_for('export_excel', company=filters.company, service=filters.service, search=filters.search, date_from=filters.date_from, date_to=filters.date_to, sort_by=filters.sort_by, sort_order=filters.sort_order) }}"
                   class="btn btn-success btn-sm">
                    <i class="fas fa-file-excel"></i> Export to Excel
                </a>
            </div>
        </div>

        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <button class="btn btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse">
                        Filters & Search
                    </button>
                </h5>
            </div>
            <div class="collapse show" id="filtersCollapse">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('submissions') }}" id="filterForm">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">Search</label>
                                <input type="text" class="form-control" name="search" 
                                       value="{{ filters.search }}" placeholder="company, service...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Company</label>
                                <select class="form-select" name="company">
                                    <option value="">All Companies</option>
                                    {% for company in companies %}
                                        <option value="{{ company }}" 
                                                {{ 'selected' if filters.company == company else '' }}>
                                            {{ company }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Service</label>
                                <select class="form-select" name="service">
                                    <option value="">All Services</option>
                                    {% for service in services %}
                                        <option value="{{ service }}" 
                                                {{ 'selected' if filters.service == service else '' }}>
                                            {{ service }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Sort By</label>
                                <select class="form-select" name="sort_by">
                                    <option value="id" {{ 'selected' if filters.sort_by == 'id' else '' }}>ID</option>
                                    <option value="created_at" {{ 'selected' if filters.sort_by == 'created_at' else '' }}>Created Date</option>
                                    <option value="company_name" {{ 'selected' if filters.sort_by == 'company_name' else '' }}>Company</option>
                                    <option value="service_name" {{ 'selected' if filters.sort_by == 'service_name' else '' }}>Service</option>
                                    <option value="price" {{ 'selected' if filters.sort_by == 'price' else '' }}>Price</option>
                                    <option value="start_date" {{ 'selected' if filters.sort_by == 'start_date' else '' }}>Start Date</option>
                                    <option value="end_date" {{ 'selected' if filters.sort_by == 'end_date' else '' }}>End Date</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Order</label>
                                <select class="form-select" name="sort_order">
                                    <option value="desc" {{ 'selected' if filters.sort_order == 'desc' else '' }}>Descending</option>
                                    <option value="asc" {{ 'selected' if filters.sort_order == 'asc' else '' }}>Ascending</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">Filter</button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="d-grid">
                                    <a href="{{ url_for('submissions') }}" class="btn btn-secondary">Clear</a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Results -->
        <!-- Deadline Legend -->
        <div class="card mb-3">
            <div class="card-body py-2">
                <div class="d-flex align-items-center justify-content-between">
                    <small class="text-muted">
                        <strong>Deadline Status:</strong>
                    </small>
                    <div class="d-flex gap-3">
                        <small class="d-flex align-items-center">
                            <span class="badge bg-danger me-1">●</span> Critical (<= 7 days)
                        </small>
                        <small class="d-flex align-items-center">
                            <span class="badge bg-warning me-1">●</span> Warning (8-15 days)
                        </small>
                        <small class="d-flex align-items-center">
                            <span class="badge bg-secondary me-1">●</span> Expired
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Orders Table Card -->
        <div class="card">
            <div class="card-body p-0">
                {% if orders.items %}
                    <div class="submissions-table-container">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Company</th>
                                        <th>Service</th>
                                        <th>Price</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Created</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in orders.items %}
                                    <tr class="{% if order.deadline_status == 'critical' %}table-danger{% elif order.deadline_status == 'warning' %}table-warning{% elif order.deadline_status == 'expired' %}table-secondary{% endif %}">
                                        <td class="fw-medium">#{{ order.id }}</td>
                                        <td>
                                            <i class="fas fa-building text-muted me-1"></i>
                                            {{ order.company_name }}
                                        </td>
                                        <td>
                                            <i class="fas fa-cog text-muted me-1"></i>
                                            {{ order.service_name }}
                                        </td>
                                        <td class="fw-medium text-success">€{{ "%.2f"|format(order.price) }}</td>
                                        <td>{{ order.start_date.strftime('%d-%m-%Y') }}</td>
                                        <td>
                                            {{ order.end_date.strftime('%d-%m-%Y') }}
                                            {% if order.deadline_status != 'normal' %}
                                                <br>
                                                <small class="badge bg-{% if order.deadline_status == 'expired' %}secondary{% elif order.deadline_status == 'critical' %}danger{% elif order.deadline_status == 'warning' %}warning{% endif %}">
                                                    {% if order.deadline_status == 'expired' %}
                                                        Expired
                                                    {% else %}
                                                        {{ order.days_until_end }} day{% if order.days_until_end != 1 %}s{% endif %} left
                                                    {% endif %}
                                                </small>
                                            {% endif %}
                                        </td>
                                        <td class="text-muted">{{ order.created_at.strftime('%d-%m-%Y %H:%M') }}</td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <a href="{{ url_for('edit_order', order_id=order.id) }}"
                                                   class="btn btn-sm btn-outline-primary" title="Edit Order">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{{ url_for('delete_order', order_id=order.id) }}"
                                                   class="btn btn-sm btn-outline-danger"
                                                   onclick="return confirm('Are you sure you want to delete this order?')"
                                                   title="Delete Order">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Orders Found</h5>
                        <p class="text-muted mb-3">No service orders match your current filters.</p>
                        <a href="{{ url_for('index') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create New Order
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Pagination -->
        {% if orders.pages > 1 %}
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                <!-- Previous page -->
                {% if orders.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('submissions', page=orders.prev_num, company=filters.company, service=filters.service, search=filters.search, date_from=filters.date_from, date_to=filters.date_to, sort_by=filters.sort_by, sort_order=filters.sort_order) }}">
                            <i class="fas fa-chevron-left"></i> Previous
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link"><i class="fas fa-chevron-left"></i> Previous</span>
                    </li>
                {% endif %}

                <!-- Page numbers -->
                {% for page_num in orders.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != orders.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('submissions', page=page_num, company=filters.company, service=filters.service, search=filters.search, date_from=filters.date_from, date_to=filters.date_to, sort_by=filters.sort_by, sort_order=filters.sort_order) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}

                <!-- Next page -->
                {% if orders.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('submissions', page=orders.next_num, company=filters.company, service=filters.service, search=filters.search, date_from=filters.date_from, date_to=filters.date_to, sort_by=filters.sort_by, sort_order=filters.sort_order) }}">
                            Next <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">Next <i class="fas fa-chevron-right"></i></span>
                    </li>
                {% endif %}
            </ul>
        </nav>

        <!-- Pagination info -->
        <div class="text-center text-muted mt-6">
            <small>
                Showing {{ orders.first }} to {{ orders.last }} of {{ orders.total }} entries
                (Page {{ orders.page }} of {{ orders.pages }})
            </small>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
